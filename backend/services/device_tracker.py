"""
خدمة تتبع الأجهزة - مبسطة لقاعدة البيانات فقط
"""

import asyncio
import json
import logging
from typing import Dict, Set, Any, Optional, List
from datetime import datetime, timedelta
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy import select, func, distinct, and_, or_
from database.session import get_db
from models.device_security import PendingDevice, ApprovedDevice, BlockedDevice
from utils.datetime_utils import get_tripoli_now

logger = logging.getLogger(__name__)

class DeviceTracker:
    """
    خدمة تتبع الأجهزة - مبسطة لقاعدة البيانات فقط
    """

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.cleanup_task: Optional[asyncio.Task] = None

    async def connect(self, websocket: WebSocket):
        """
        إضافة اتصال WebSocket جديد
        """
        await websocket.accept()
        self.active_connections.add(websocket)

        # إرسال البيانات الحالية للعميل الجديد
        await self.send_current_devices(websocket)

    async def disconnect(self, websocket: WebSocket):
        """
        إزالة اتصال WebSocket
        """
        self.active_connections.discard(websocket)

        # إيقاف مهمة التنظيف إذا لم تعد هناك اتصالات
        if not self.active_connections and self.cleanup_task:
            self.cleanup_task.cancel()
            self.cleanup_task = None

    async def send_current_devices(self, websocket: WebSocket):
        """
        إرسال البيانات الحالية للعميل
        """
        try:
            devices_data = await self.get_devices_from_database()
            await websocket.send_text(json.dumps({
                'type': 'devices_update',
                'data': devices_data,
                'timestamp': datetime.now().isoformat()
            }))
        except Exception as e:
            logger.error(f"خطأ في إرسال البيانات: {e}")

    async def broadcast_device_update(self):
        """
        إرسال تحديث لجميع العملاء المتصلين
        """
        if not self.active_connections:
            return

        try:
            devices_data = await self.get_devices_from_database()
            message = json.dumps({
                'type': 'devices_update',
                'data': devices_data,
                'timestamp': datetime.now().isoformat()
            })

            # إرسال لجميع الاتصالات النشطة
            disconnected = set()
            for websocket in self.active_connections:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.warning(f"فشل في إرسال التحديث: {e}")
                    disconnected.add(websocket)

            # إزالة الاتصالات المنقطعة
            for websocket in disconnected:
                self.active_connections.discard(websocket)

        except Exception as e:
            logger.error(f"خطأ في إرسال التحديث: {e}")

    async def register_device_activity(self, client_ip: str, user_agent: str = "", current_user: Optional[str] = None, request_headers: Optional[dict] = None):
        """
        تسجيل نشاط جهاز - مبسط مع تحديث قاعدة البيانات
        """
        try:
            # تحديث آخر وصول في قاعدة البيانات
            await self._update_device_last_access(client_ip, current_user)

            # إشعار جميع العملاء المتصلين
            await self.broadcast_device_update()
        except Exception as e:
            logger.debug(f"خطأ في تسجيل نشاط الجهاز: {e}")

    async def register_remote_device_activity(self, client_ip: str, user_agent: str = "", current_user: Optional[str] = None, request_headers: Optional[dict] = None):
        """
        تسجيل نشاط جهاز بعيد - مبسط مع تحديث قاعدة البيانات
        """
        try:
            # تحديث آخر وصول في قاعدة البيانات
            await self._update_device_last_access(client_ip, current_user)

            # إشعار جميع العملاء المتصلين
            await self.broadcast_device_update()
        except Exception as e:
            logger.debug(f"خطأ في تسجيل نشاط الجهاز البعيد: {e}")

    async def _update_device_last_access(self, client_ip: str, current_user: Optional[str] = None):
        """
        تحديث آخر وصول للجهاز في قاعدة البيانات
        """
        try:
            db = next(get_db())
            current_time = get_tripoli_now()

            # البحث عن الجهاز في الأجهزة المعتمدة
            stmt = select(ApprovedDevice).where(ApprovedDevice.client_ip == client_ip)
            device = db.execute(stmt).scalar_one_or_none()

            if device:
                # تحديث آخر وصول وعدد مرات الوصول
                device.last_access = current_time
                device.access_count = (device.access_count or 0) + 1

                # تحديث المستخدم الحالي إذا تم توفيره
                if current_user:
                    device.current_user = current_user

                db.commit()
                logger.debug(f"تم تحديث آخر وصول للجهاز: {client_ip}")
            else:
                # البحث في الأجهزة المنتظرة
                pending_stmt = select(PendingDevice).where(PendingDevice.client_ip == client_ip)
                pending_device = db.execute(pending_stmt).scalar_one_or_none()

                if pending_device:
                    # تحديث آخر وصول للجهاز المنتظر
                    pending_device.last_access = current_time
                    pending_device.access_count = (pending_device.access_count or 0) + 1

                    if current_user:
                        pending_device.current_user = current_user

                    db.commit()
                    logger.debug(f"تم تحديث آخر وصول للجهاز المنتظر: {client_ip}")

        except Exception as e:
            logger.error(f"خطأ في تحديث آخر وصول للجهاز: {e}")
        finally:
            if 'db' in locals():
                db.close()

    async def _force_update_device_user(self, client_ip: str, current_user: Optional[str] = None):
        """
        إجبار تحديث المستخدم الحالي للجهاز - للاستخدام عند تبديل المستخدم
        """
        try:
            db = next(get_db())
            current_time = get_tripoli_now()

            # البحث عن الجهاز في الأجهزة المعتمدة
            stmt = select(ApprovedDevice).where(ApprovedDevice.client_ip == client_ip)
            device = db.execute(stmt).scalar_one_or_none()

            if device:
                # تحديث المستخدم الحالي وآخر وصول
                device.current_user = current_user
                device.last_access = current_time
                device.access_count = (device.access_count or 0) + 1
                db.commit()
                logger.info(f"✅ تم إجبار تحديث المستخدم للجهاز: {client_ip} -> {current_user}")
            else:
                # البحث في الأجهزة المنتظرة
                pending_stmt = select(PendingDevice).where(PendingDevice.client_ip == client_ip)
                pending_device = db.execute(pending_stmt).scalar_one_or_none()

                if pending_device:
                    pending_device.current_user = current_user
                    pending_device.last_access = current_time
                    pending_device.access_count = (pending_device.access_count or 0) + 1
                    db.commit()
                    logger.info(f"✅ تم إجبار تحديث المستخدم للجهاز المنتظر: {client_ip} -> {current_user}")

        except Exception as e:
            logger.error(f"خطأ في إجبار تحديث المستخدم للجهاز: {e}")
        finally:
            if 'db' in locals():
                db.close()

    async def _delayed_broadcast_update(self, delay_seconds: float = 0.1):  # ✅ تقليل التأخير لتحديث أسرع
        """
        إرسال تحديث مؤجل لجميع العملاء المتصلين - محسن للاستجابة السريعة
        """
        try:
            import asyncio
            await asyncio.sleep(delay_seconds)
            await self.broadcast_device_update()
            logger.debug(f"✅ تم إرسال تحديث مؤجل بعد {delay_seconds} ثانية")
        except Exception as e:
            logger.error(f"خطأ في التحديث المؤجل: {e}")

    async def immediate_broadcast_update(self):
        """
        إرسال تحديث فوري بدون تأخير - للتحديثات الحرجة
        """
        try:
            await self.broadcast_device_update()
            logger.debug("✅ تم إرسال تحديث فوري")
        except Exception as e:
            logger.error(f"خطأ في التحديث الفوري: {e}")

    async def _remove_main_server_from_approved(self, db):
        """
        إزالة الخادم الرئيسي من جدول الأجهزة المعتمدة (يجب أن يكون مستثنى)
        """
        try:
            # قائمة معرفات الخادم الرئيسي
            main_server_ids = [
                'main_server_primary',
                'main_server_6b74625ff918',
                'fp_3tae9f'
            ]

            # حذف جميع إدخالات الخادم الرئيسي من الأجهزة المعتمدة
            for server_id in main_server_ids:
                stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == server_id)
                device = db.execute(stmt).scalar_one_or_none()
                if device:
                    db.delete(device)
                    logger.info(f"✅ تم حذف الخادم الرئيسي من الأجهزة المعتمدة: {server_id}")

            # حذف أيضاً بناءً على IP الخادم الرئيسي
            stmt = select(ApprovedDevice).where(ApprovedDevice.client_ip == '*************')
            main_server_by_ip = db.execute(stmt).scalar_one_or_none()
            if main_server_by_ip:
                db.delete(main_server_by_ip)
                logger.info(f"✅ تم حذف الخادم الرئيسي من الأجهزة المعتمدة بناءً على IP: *************")

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في إزالة الخادم الرئيسي من الأجهزة المعتمدة: {e}")
            db.rollback()

    async def _cleanup_main_server_from_pending(self, db):
        """
        تنظيف أجهزة الخادم الرئيسي من قائمة الانتظار
        """
        try:
            # قائمة معرفات الخادم الرئيسي
            main_server_ids = [
                'main_server_primary',
                'main_server_6b74625ff918',
                'fp_3tae9f'
            ]

            # حذف جميع إدخالات الخادم الرئيسي من قائمة الانتظار
            for server_id in main_server_ids:
                stmt = select(PendingDevice).where(PendingDevice.device_id == server_id)
                device = db.execute(stmt).scalar_one_or_none()
                if device:
                    db.delete(device)
                    logger.info(f"✅ تم حذف الخادم الرئيسي من قائمة الانتظار: {server_id}")

            # حذف أيضاً بناءً على IP الخادم الرئيسي
            stmt = select(PendingDevice).where(PendingDevice.client_ip == '*************')
            pending_main_server = db.execute(stmt).scalar_one_or_none()
            if pending_main_server:
                db.delete(pending_main_server)
                logger.info(f"✅ تم حذف الخادم الرئيسي من قائمة الانتظار بناءً على IP: *************")

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في تنظيف الخادم الرئيسي من قائمة الانتظار: {e}")
            db.rollback()

    async def _update_approved_devices_from_fingerprints(self, db):
        """
        تحديث بيانات الأجهزة المعتمدة من جدول البصمة لضمان دقة البيانات
        """
        try:
            from models.device_fingerprint import DeviceFingerprint

            # جلب جميع الأجهزة المعتمدة التي تحتوي على بصمة متقدمة
            approved_stmt = select(ApprovedDevice).where(ApprovedDevice.device_id.like('fp_%'))
            approved_devices = db.execute(approved_stmt).scalars().all()

            updated_count = 0
            for device in approved_devices:
                try:
                    # البحث عن البصمة المقابلة
                    fingerprint_stmt = select(DeviceFingerprint).where(DeviceFingerprint.fingerprint_id == device.device_id)
                    fingerprint = db.execute(fingerprint_stmt).scalar_one_or_none()

                    if fingerprint:
                        # استخراج البيانات من معلومات البصمة
                        import json

                        # جلب معلومات الجهاز من JSON
                        device_info = {}
                        system_info = {}

                        if fingerprint.device_info:
                            try:
                                device_info = json.loads(fingerprint.device_info)
                            except:
                                device_info = {}

                        if fingerprint.system_info:
                            try:
                                system_info = json.loads(fingerprint.system_info)
                            except:
                                system_info = {}

                        # تحديث البيانات إذا كانت فارغة أو "غير معروف"
                        updated = False

                        # تحديث اسم الجهاز من user_agent
                        user_agent = device_info.get('user_agent', '')
                        if (not device.hostname or device.hostname.strip() in ['', 'جهاز غير معروف']):
                            if 'Windows' in user_agent:
                                device.hostname = 'جهاز Windows'
                                updated = True
                            elif 'Linux' in user_agent:
                                device.hostname = 'جهاز Linux'
                                updated = True
                            elif 'Mac' in user_agent:
                                device.hostname = 'جهاز Mac'
                                updated = True

                        # تحديث النظام من system_fingerprint
                        if (not device.system or device.system.strip() in ['', 'غير معروف']):
                            system_fp = system_info.get('system_fingerprint', '')
                            if 'Windows' in system_fp:
                                device.system = 'Windows'
                                updated = True
                            elif 'Linux' in system_fp:
                                device.system = 'Linux'
                                updated = True
                            elif 'Mac' in system_fp:
                                device.system = 'macOS'
                                updated = True

                        # تحديث نوع الجهاز
                        if (not device.device_type or device.device_type.strip() in ['', 'غير محدد']):
                            if 'Windows' in user_agent:
                                device.device_type = 'جهاز Windows'
                                updated = True
                            elif 'Linux' in user_agent:
                                device.device_type = 'جهاز Linux'
                                updated = True

                        if updated:
                            updated_count += 1
                            logger.info(f"✅ تم تحديث بيانات الجهاز المعتمد من البصمة: {device.device_id}")

                except Exception as device_error:
                    logger.warning(f"⚠️ فشل في تحديث بيانات الجهاز {device.device_id}: {device_error}")

            if updated_count > 0:
                db.commit()
                logger.info(f"✅ تم تحديث {updated_count} جهاز معتمد من بيانات البصمة")

        except Exception as e:
            logger.error(f"خطأ في تحديث الأجهزة المعتمدة من البصمة: {e}")
            db.rollback()

    async def get_devices_from_database(self) -> Dict[str, Any]:
        """
        جلب بيانات الأجهزة من قاعدة البيانات فقط
        """
        try:
            db = next(get_db())
            current_time = get_tripoli_now()

            # ✅ إزالة الخادم الرئيسي من جدول الأجهزة المعتمدة (يجب أن يكون مستثنى)
            await self._remove_main_server_from_approved(db)

            # ✅ تنظيف أجهزة الخادم الرئيسي من قائمة الانتظار
            await self._cleanup_main_server_from_pending(db)

            # ✅ تحديث بيانات الأجهزة المعتمدة من جدول البصمة
            await self._update_approved_devices_from_fingerprints(db)

            # جلب الأجهزة المعتمدة
            approved_stmt = select(ApprovedDevice)
            approved_devices = db.execute(approved_stmt).scalars().all()

            # جلب الأجهزة المنتظرة
            pending_stmt = select(PendingDevice).where(PendingDevice.status == 'pending')
            pending_devices = db.execute(pending_stmt).scalars().all()

            # جلب الأجهزة المحظورة
            blocked_stmt = select(BlockedDevice)
            blocked_devices = db.execute(blocked_stmt).scalars().all()

            devices = []

            # معالجة الأجهزة المعتمدة
            for device in approved_devices:
                # تحديد ما إذا كان الجهاز خادم رئيسي
                device_id_str = str(device.device_id)
                client_ip_str = str(device.client_ip)
                is_main_server = device_id_str in ['main_server_primary', 'main_server_6b74625ff918', 'fp_3tae9f'] or client_ip_str == '*************'
                is_local_access = client_ip_str in ['127.0.0.1', 'localhost', '*************']

                # تحديد ما إذا كان الجهاز يحتوي على بصمة متقدمة
                is_advanced_fingerprint = False
                if not is_main_server:  # ✅ الآن is_main_server هو boolean عادي
                    # فحص وجود بصمة متقدمة في قاعدة البيانات
                    try:
                        from models.device_fingerprint import DeviceFingerprint
                        fingerprint_stmt = select(DeviceFingerprint).where(DeviceFingerprint.fingerprint_id == device.device_id)
                        fingerprint = db.execute(fingerprint_stmt).scalar_one_or_none()
                        is_advanced_fingerprint = fingerprint is not None
                    except Exception:
                        is_advanced_fingerprint = False

                # ✅ تحسين عرض اسم الجهاز بربط البيانات من جدول البصمة
                device_hostname = device.hostname or ''
                device_system = device.system or ''
                device_platform = device.platform or ''
                device_type = device.device_type or ''

                # ✅ محاولة جلب البيانات من جدول البصمة إذا كان الجهاز يحتوي على بصمة متقدمة
                if is_advanced_fingerprint:
                    try:
                        fingerprint_stmt = select(DeviceFingerprint).where(DeviceFingerprint.fingerprint_id == device.device_id)
                        fingerprint = db.execute(fingerprint_stmt).scalar_one_or_none()

                        if fingerprint:
                            # استخراج البيانات من معلومات البصمة
                            import json

                            # جلب معلومات الجهاز من JSON
                            device_info = {}
                            system_info = {}

                            if fingerprint.device_info:
                                try:
                                    device_info = json.loads(fingerprint.device_info)
                                except:
                                    device_info = {}

                            if fingerprint.system_info:
                                try:
                                    system_info = json.loads(fingerprint.system_info)
                                except:
                                    system_info = {}

                            # استخراج اسم الجهاز من user_agent أو headers
                            user_agent = device_info.get('user_agent', '')
                            if 'Windows' in user_agent and not device_hostname.strip():
                                device_hostname = 'جهاز Windows'
                            elif 'Linux' in user_agent and not device_hostname.strip():
                                device_hostname = 'جهاز Linux'
                            elif 'Mac' in user_agent and not device_hostname.strip():
                                device_hostname = 'جهاز Mac'

                            # تحديث النظام من system_fingerprint
                            if system_info.get('system_fingerprint') and not device_system.strip():
                                system_fp = system_info['system_fingerprint']
                                if 'Windows' in system_fp:
                                    device_system = 'Windows'
                                elif 'Linux' in system_fp:
                                    device_system = 'Linux'
                                elif 'Mac' in system_fp:
                                    device_system = 'macOS'

                            # تحديث نوع الجهاز
                            if 'Windows' in user_agent and not device_type.strip():
                                device_type = 'جهاز Windows'
                            elif 'Linux' in user_agent and not device_type.strip():
                                device_type = 'جهاز Linux'

                            logger.debug(f"✅ تم تحديث بيانات الجهاز من البصمة: {device.device_id}")
                    except Exception as e:
                        logger.debug(f"⚠️ فشل في جلب بيانات البصمة: {e}")

                # إذا لم تتوفر البيانات، استخدم الاستخراج من user_agent
                if not device_hostname.strip():
                    user_agent_str = device.user_agent or ''
                    client_ip_str = device.client_ip or ''

                    if 'Windows' in user_agent_str:
                        device_hostname = 'جهاز Windows'
                    elif 'Linux' in user_agent_str:
                        device_hostname = 'جهاز Linux'
                    elif 'Mac' in user_agent_str:
                        device_hostname = 'جهاز Mac'
                    elif client_ip_str.strip():
                        device_hostname = f'جهاز {client_ip_str}'
                    else:
                        device_hostname = 'جهاز بعيد'

                device_data = {
                    'device_id': device.device_id,
                    'client_ip': device.client_ip,
                    'hostname': device_hostname,  # ✅ استخدام الاسم المحسن من البصمة
                    'device_type': device_type or 'جهاز بعيد',  # ✅ استخدام النوع المحسن من البصمة
                    'system': device_system or 'غير معروف',  # ✅ استخدام النظام المحسن من البصمة
                    'platform': device_platform or 'غير معروف',  # ✅ استخدام المنصة المحسنة من البصمة
                    'user_agent': device.user_agent or '',
                    'current_user': device.current_user or '',
                    'first_access': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.first_access)),
                    'last_access': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.last_access)),
                    'access_count': device.access_count or 0,
                    'status': self._calculate_device_status(self._safe_get_datetime(device.last_access), current_time),
                    'requires_approval': False,
                    'is_approved': True,
                    'approval_status': 'approved',  # إضافة حالة الاعتماد للواجهة الأمامية
                    'approved_by': device.approved_by,
                    'approved_at': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.approved_at)),
                    'is_main_server': is_main_server,
                    'is_local_access': is_local_access,
                    'is_advanced_fingerprint': is_advanced_fingerprint  # ✅ إضافة معلومات البصمة المتقدمة
                }
                devices.append(device_data)

            # معالجة الأجهزة المنتظرة
            for device in pending_devices:
                # تحديد ما إذا كان الجهاز يحتوي على بصمة متقدمة
                is_advanced_fingerprint = False
                try:
                    from models.device_fingerprint import DeviceFingerprint
                    fingerprint_stmt = select(DeviceFingerprint).where(DeviceFingerprint.fingerprint_id == device.device_id)
                    fingerprint = db.execute(fingerprint_stmt).scalar_one_or_none()
                    is_advanced_fingerprint = fingerprint is not None
                except Exception:
                    is_advanced_fingerprint = False

                device_data = {
                    'device_id': device.device_id,
                    'client_ip': device.client_ip,
                    'hostname': device.hostname or 'جهاز في انتظار الموافقة',
                    'device_type': device.device_type or 'غير محدد',
                    'system': device.system or 'غير معروف',
                    'platform': device.platform or 'غير معروف',
                    'user_agent': device.user_agent or '',
                    'current_user': device.current_user or '',
                    'first_access': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.first_access)),
                    'last_access': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.last_access)),
                    'access_count': device.access_count or 0,
                    'status': 'pending_approval',
                    'requires_approval': True,
                    'is_approved': False,
                    'approval_status': 'pending',  # إضافة حالة الاعتماد للواجهة الأمامية
                    'requested_at': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.requested_at)),
                    'is_main_server': False,
                    'is_local_access': False,
                    'is_advanced_fingerprint': is_advanced_fingerprint  # ✅ إضافة معلومات البصمة المتقدمة
                }
                devices.append(device_data)

            # ✅ معالجة الأجهزة المحظورة - إضافة جديدة
            for device in blocked_devices:
                # تحديد ما إذا كان الجهاز يحتوي على بصمة متقدمة
                is_advanced_fingerprint = False
                try:
                    from models.device_fingerprint import DeviceFingerprint
                    fingerprint_stmt = select(DeviceFingerprint).where(DeviceFingerprint.fingerprint_id == device.device_id)
                    fingerprint = db.execute(fingerprint_stmt).scalar_one_or_none()
                    is_advanced_fingerprint = fingerprint is not None
                except Exception:
                    is_advanced_fingerprint = False

                device_data = {
                    'device_id': device.device_id,
                    'client_ip': device.client_ip,
                    'hostname': device.hostname or 'جهاز محظور',
                    'device_type': device.device_type or 'جهاز محظور',
                    'system': device.system or 'غير معروف',
                    'platform': device.platform or 'غير معروف',
                    'user_agent': device.user_agent or '',
                    'current_user': '',  # الأجهزة المحظورة لا تحتوي على مستخدم حالي
                    'first_access': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.first_access)),
                    'last_access': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.last_access)),
                    'access_count': device.access_count or 0,
                    'status': 'blocked',  # ✅ حالة خاصة للأجهزة المحظورة
                    'is_main_server': False,
                    'is_local_access': False,
                    'is_approved': False,
                    'approval_status': 'blocked',  # ✅ حالة الاعتماد محظور
                    'is_advanced_fingerprint': is_advanced_fingerprint,
                    'security_level': 'blocked',
                    'security_level_ar': 'محظور',
                    # ✅ معلومات الحظر الإضافية
                    'blocked_by': device.blocked_by,
                    'blocked_at': (lambda dt: dt.isoformat() if dt else None)(self._safe_get_datetime(device.blocked_at)),
                    'block_reason': device.block_reason or 'محظور بواسطة المدير',
                    'is_blocked': True  # ✅ علامة واضحة للأجهزة المحظورة
                }
                devices.append(device_data)

            # حساب الإحصائيات بدقة
            total_devices = len(devices)
            online_devices = len([d for d in devices if d['status'] == 'online'])
            recently_active_devices = len([d for d in devices if d['status'] == 'recently_active'])
            offline_devices = len([d for d in devices if d['status'] == 'offline'])
            pending_approval_devices = len([d for d in devices if d['status'] == 'pending_approval'])
            blocked_devices_active_count = len([d for d in devices if d['status'] == 'blocked'])  # ✅ الأجهزة المحظورة النشطة في القائمة
            main_server_count = len([d for d in devices if d.get('is_main_server', False)])

            # ✅ إصلاح حساب الأجهزة المحلية والبعيدة
            local_devices_count = len([d for d in devices if d.get('is_local_access', False) and not d.get('is_main_server', False)])
            remote_devices_count = len([d for d in devices if not d.get('is_local_access', False) and not d.get('is_main_server', False)])

            # ✅ إضافة إحصائيات الأمان المحسنة
            approved_devices_count = len([d for d in devices if d.get('is_approved', False)])
            pending_devices_count = len([d for d in devices if d.get('approval_status') == 'pending'])
            blocked_devices_count = len(blocked_devices)  # من قاعدة البيانات
            advanced_fingerprint_count = len([d for d in devices if d.get('is_advanced_fingerprint', False)])

            return {
                'success': True,
                'devices': devices,
                'summary': {
                    'total_devices': total_devices,
                    'main_server_count': main_server_count,
                    'local_devices_count': local_devices_count,
                    'remote_devices_count': remote_devices_count,
                    'online_devices': online_devices,
                    'recently_active_devices': recently_active_devices,
                    'offline_devices': offline_devices,
                    'pending_approval_devices': pending_approval_devices,
                    'blocked_devices_count': blocked_devices_count,
                    'pending_devices_count': pending_devices_count,
                    'approved_devices_count': approved_devices_count,
                    'advanced_fingerprint_count': advanced_fingerprint_count  # ✅ إضافة إحصائية البصمات المتقدمة
                },
                'last_updated': current_time.isoformat(),
                'data_source': 'database_enhanced',
                'data_consistency_check': True  # ✅ إشارة لتأكيد تناسق البيانات
            }

        except Exception as e:
            logger.error(f"خطأ في جلب البيانات من قاعدة البيانات: {e}")
            return {
                'success': False,
                'devices': [],
                'summary': {
                    'total_devices': 0,
                    'main_server_count': 0,
                    'local_devices_count': 0,
                    'remote_devices_count': 0,
                    'online_devices': 0,
                    'recently_active_devices': 0,
                    'offline_devices': 0,
                    'pending_approval_devices': 0,
                    'blocked_devices_count': 0,
                    'pending_devices_count': 0,
                    'approved_devices_count': 0
                },
                'error': str(e),
                'data_source': 'database_error'
            }

    def _safe_get_datetime(self, value) -> Optional[datetime]:
        """
        استخراج قيمة datetime بأمان من SQLAlchemy objects
        """
        if value is None:
            return None
        if hasattr(value, 'isoformat'):  # datetime object
            return value
        return None

    def _calculate_device_status(self, last_access: Optional[datetime], current_time: datetime) -> str:
        """
        حساب حالة الجهاز بناءً على آخر وصول
        """
        if not last_access:
            return 'offline'

        try:
            # التأكد من أن كلا التاريخين لهما نفس timezone
            if last_access.tzinfo is None:
                last_access = last_access.replace(tzinfo=current_time.tzinfo)
            elif current_time.tzinfo is None:
                current_time = current_time.replace(tzinfo=last_access.tzinfo)

            time_diff = current_time - last_access

            if time_diff.total_seconds() <= 30:  # أقل من 30 ثانية
                return 'online'
            elif time_diff.total_seconds() <= 300:  # أقل من 5 دقائق
                return 'recently_active'
            else:
                return 'offline'
        except Exception as e:
            logger.debug(f"خطأ في حساب حالة الجهاز: {e}")
            return 'offline'

    async def _ensure_main_server_exists(self, db, current_time):
        """
        التأكد من وجود الخادم الرئيسي في قاعدة البيانات
        """
        try:
            # قائمة معرفات الخادم الرئيسي المحتملة
            main_server_ids = [
                'main_server_primary',
                'main_server_6b74625ff918',
                'fp_3tae9f'
            ]

            # البحث عن أي من معرفات الخادم الرئيسي
            main_server = None
            for server_id in main_server_ids:
                stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == server_id)
                found_server = db.execute(stmt).scalar_one_or_none()
                if found_server:
                    main_server = found_server
                    break

            # البحث أيضاً بناءً على IP الخادم الرئيسي
            if not main_server:
                stmt = select(ApprovedDevice).where(ApprovedDevice.client_ip == '*************')
                main_server = db.execute(stmt).scalar_one_or_none()

            if not main_server:
                # إنشاء الخادم الرئيسي إذا لم يكن موجوداً
                main_server = ApprovedDevice(
                    device_id='main_server_primary',
                    client_ip='*************',
                    hostname='Chiqwa-GL65-Leopard-10SDR',
                    device_type='خادم رئيسي',
                    system='Linux',
                    platform='Linux-6.11.0-28-generic-x86_64-with-glibc2.39',
                    user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    current_user='admin',
                    first_access=current_time,
                    last_access=current_time,
                    access_count=1,
                    approved_by='system',
                    approved_at=current_time,
                    created_at=current_time,
                    updated_at=current_time
                )
                db.add(main_server)
                db.commit()
                logger.info("تم إنشاء الخادم الرئيسي في قاعدة البيانات")
            else:
                # تحديث آخر وصول للخادم الرئيسي
                main_server.last_access = current_time
                main_server.access_count = (main_server.access_count or 0) + 1
                db.commit()

        except Exception as e:
            logger.error(f"خطأ في التأكد من وجود الخادم الرئيسي: {e}")

    async def _auto_approve_main_server_devices(self, db, current_time):
        """
        نقل أجهزة الخادم الرئيسي من قائمة الانتظار إلى المعتمدة تلقائياً
        """
        try:
            # البحث عن أجهزة الخادم الرئيسي في قائمة الانتظار
            pending_stmt = select(PendingDevice).where(
                or_(
                    PendingDevice.client_ip == '*************',
                    PendingDevice.device_id.in_(['main_server_primary', 'main_server_6b74625ff918', 'fp_3tae9f'])
                )
            )
            pending_main_servers = db.execute(pending_stmt).scalars().all()

            for pending_device in pending_main_servers:
                # التحقق من عدم وجود الجهاز في قائمة المعتمدة
                approved_stmt = select(ApprovedDevice).where(
                    or_(
                        ApprovedDevice.device_id == pending_device.device_id,
                        ApprovedDevice.client_ip == pending_device.client_ip
                    )
                )
                existing_approved = db.execute(approved_stmt).scalar_one_or_none()

                if not existing_approved:
                    # إنشاء جهاز معتمد جديد
                    approved_device = ApprovedDevice(
                        device_id=pending_device.device_id,
                        client_ip=pending_device.client_ip,
                        hostname=pending_device.hostname or 'خادم رئيسي',
                        device_type='خادم رئيسي',
                        system=pending_device.system or 'Linux',
                        platform=pending_device.platform or 'غير معروف',
                        user_agent=pending_device.user_agent or '',
                        current_user=pending_device.current_user or 'admin',
                        first_access=pending_device.first_access or current_time,
                        last_access=current_time,
                        access_count=pending_device.access_count or 1,
                        approved_by='system_auto',
                        approved_at=current_time,
                        created_at=current_time,
                        updated_at=current_time
                    )
                    db.add(approved_device)

                    # حذف من قائمة الانتظار
                    db.delete(pending_device)

                    logger.info(f"تم نقل الخادم الرئيسي تلقائياً من الانتظار إلى المعتمدة: {pending_device.device_id}")

            db.commit()

        except Exception as e:
            logger.error(f"خطأ في نقل أجهزة الخادم الرئيسي تلقائياً: {e}")
            db.rollback()

    async def _force_approve_main_server(self, db, current_time):
        """
        إجبار اعتماد الخادم الرئيسي إذا كان في قائمة الانتظار
        """
        try:
            # البحث عن الخادم الرئيسي في قائمة الانتظار بناءً على IP
            pending_stmt = select(PendingDevice).where(PendingDevice.client_ip == '*************')
            pending_main_server = db.execute(pending_stmt).scalar_one_or_none()

            if pending_main_server:
                logger.info(f"🔧 إجبار اعتماد الخادم الرئيسي: {pending_main_server.device_id}")

                # التحقق من عدم وجوده في قائمة المعتمدة
                approved_stmt = select(ApprovedDevice).where(
                    or_(
                        ApprovedDevice.device_id == pending_main_server.device_id,
                        ApprovedDevice.client_ip == '*************'
                    )
                )
                existing_approved = db.execute(approved_stmt).scalar_one_or_none()

                if not existing_approved:
                    # إنشاء جهاز معتمد جديد
                    approved_device = ApprovedDevice(
                        device_id=pending_main_server.device_id,
                        client_ip='*************',
                        hostname='خادم رئيسي',
                        device_type='خادم رئيسي',
                        system=pending_main_server.system or 'Linux',
                        platform=pending_main_server.platform or 'غير معروف',
                        user_agent=pending_main_server.user_agent or '',
                        current_user='admin',
                        first_access=pending_main_server.first_access or current_time,
                        last_access=current_time,
                        access_count=pending_main_server.access_count or 1,
                        approved_by='system_force',
                        approved_at=current_time,
                        created_at=current_time,
                        updated_at=current_time
                    )
                    db.add(approved_device)

                    # حذف من قائمة الانتظار
                    db.delete(pending_main_server)

                    db.commit()
                    logger.info(f"✅ تم إجبار اعتماد الخادم الرئيسي: {pending_main_server.device_id}")
                else:
                    # حذف من قائمة الانتظار فقط
                    db.delete(pending_main_server)
                    db.commit()
                    logger.info(f"🗑️ تم حذف الخادم الرئيسي من قائمة الانتظار (موجود في المعتمدة): {pending_main_server.device_id}")

        except Exception as e:
            logger.error(f"خطأ في إجبار اعتماد الخادم الرئيسي: {e}")
            db.rollback()

    async def cleanup_inactive_devices(self):
        """
        تنظيف دوري للأجهزة غير النشطة
        """
        while True:
            try:
                await asyncio.sleep(60)  # كل دقيقة

                # إرسال تحديث دوري للعملاء
                await self.broadcast_device_update()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"خطأ في تنظيف الأجهزة: {e}")
                await asyncio.sleep(5)

# إنشاء مثيل واحد للاستخدام العام
device_tracker = DeviceTracker()
